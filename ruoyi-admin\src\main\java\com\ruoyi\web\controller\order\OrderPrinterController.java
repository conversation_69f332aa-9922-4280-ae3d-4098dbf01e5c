package com.ruoyi.web.controller.order;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;


import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.order.domain.OrderPrinter;
import com.ruoyi.order.domain.OrderPrinterTask;
import com.ruoyi.order.dto.OrderUpdateRequest;
import com.ruoyi.order.dto.UploadFileRequest;
import com.ruoyi.order.service.IOrderPrinterService;
import com.ruoyi.order.service.IOrderPrinterBusinessService;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.service.WxAppAuthService;
import com.yunchuang.wxapp.util.UserContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



/**
 * 订单打印机控制器
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@RestController
@RequestMapping("/order/printer")
public class OrderPrinterController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(OrderPrinterController.class);

    @Autowired
    private IOrderPrinterService orderPrinterService;

    @Autowired
    private IOrderPrinterBusinessService orderPrinterBusinessService;

    @Autowired
    private WxAppAuthService wxAppAuthService;





    /**
     * 查询订单打印机列表
     */
//    @PreAuthorize("@ss.hasPermi('printer:order:list')")
//    @Anonymous
    @GetMapping("/list")
    public TableDataInfo list(OrderPrinter orderPrinter)
    {
        startPage();
        List<OrderPrinter> list = orderPrinterService.selectOrderPrinterList(orderPrinter);
        return getDataTable(list);
    }

    /**
     * 导出订单打印机列表
     */
//    @PreAuthorize("@ss.hasPermi('order:printer:export')")
    @Log(title = "订单打印机", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrderPrinter orderPrinter)
    {
        List<OrderPrinter> list = orderPrinterService.selectOrderPrinterList(orderPrinter);
        ExcelUtil<OrderPrinter> util = new ExcelUtil<OrderPrinter>(OrderPrinter.class);
        util.exportExcel(response, list, "订单打印机数据");
    }

    /**
     * 获取订单打印机详细信息
     */
//    @PreAuthorize("@ss.hasPermi('printer:order:query')")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") String orderId)
    {
        return success(orderPrinterService.selectOrderPrinterByOrderId(orderId));
    }

    /**
     * 新增订单打印机
     */
//    @PreAuthorize("@ss.hasPermi('order:printer:add')")
    @Log(title = "订单打印机", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OrderPrinter orderPrinter)
    {
        return toAjax(orderPrinterService.insertOrderPrinter(orderPrinter));
    }

    /**
     * 修改订单打印机
     */
//    @PreAuthorize("@ss.hasPermi('order:printer:edit')")
    @Log(title = "订单打印机", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrderPrinter orderPrinter)
    {
        return toAjax(orderPrinterService.updateOrderPrinter(orderPrinter));
    }

    /**
     * 删除订单打印机
     */
//    @PreAuthorize("@ss.hasPermi('order:printer:remove')")
    @Log(title = "订单打印机", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable String[] orderIds)
    {
        return toAjax(orderPrinterService.deleteOrderPrinterByOrderIds(orderIds));
    }
    
   
    
    /**
     * 支付回调处理
     */
    @PostMapping("/payNotify")
    public AjaxResult payNotify(@RequestBody Map<String, Object> params) {
        // 校验必要参数
        String orderId = (String) params.get("orderId");
        String transactionId = (String) params.get("transactionId");
        
        if (StringUtils.isEmpty(orderId)) {
            return AjaxResult.error("订单ID不能为空");
        }
        
        try {
            // 处理支付成功
            boolean result = orderPrinterService.paySuccess(orderId, transactionId);
            if (result) {
                return AjaxResult.success("支付处理成功");
            } else {
                return AjaxResult.error("支付处理失败，订单不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("支付处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询订单打印任务
     */
    @GetMapping("/tasks/{orderId}")
    public AjaxResult getOrderTasks(@PathVariable("orderId") String orderId) {
        List<OrderPrinterTask> tasks = orderPrinterService.getOrderTasks(orderId);
        return AjaxResult.success(tasks);
    }
    
    /**
     * 更新打印任务状态
     */
    @PostMapping("/task/status")
    public AjaxResult updateTaskStatus(@RequestBody Map<String, Object> params) {
        // 校验必要参数
        String taskId = (String) params.get("taskId");
        Integer status = (Integer) params.get("status");
        String errorMsg = (String) params.get("errorMsg");
        
        if (StringUtils.isEmpty(taskId) || status == null) {
            return AjaxResult.error("任务ID和状态不能为空");
        }
        
        try {
            // 更新任务状态
            int result = orderPrinterService.updateTaskStatus(taskId, status, errorMsg);
            if (result > 0) {
                return AjaxResult.success("更新状态成功");
            } else {
                return AjaxResult.error("更新状态失败，任务不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("更新状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询用户订单列表
     * @param request HTTP请求
     * @param orderStatus 订单状态筛选：0-未支付，1-已支付，2-已取消，3-已退款，4-已打印，5-打印中，6-打印失败，不传查询所有
     *                   特殊处理：传入"1,5"表示进行中状态（已支付+打印中）
     */
    @GetMapping("/user")
    public AjaxResult getUserOrders(
            HttpServletRequest request,
            @RequestParam(value = "orderStatus", required = false) String orderStatus) {
        // 从token中获取openid
        String openid = getCurrentUserOpenid(request);
        if (StringUtils.isEmpty(openid)) {
            return AjaxResult.error("用户未登录或openid获取失败");
        }

        log.info("查询用户订单列表，openid: {}, orderStatus: {}", openid, orderStatus);

        // 使用新的方法，支持订单状态筛选
        List<OrderPrinter> orders = orderPrinterService.selectOrderPrinterListByOrderStatus(openid, orderStatus);

        log.info("查询到{}个订单", orders != null ? orders.size() : 0);

        return AjaxResult.success(orders);
    }
    
    /**
     * 获取用户订单详情（包含打印任务）
     */
    @Anonymous
    @GetMapping("/user/detail/{orderId}")
    public AjaxResult getOrderDetail(@PathVariable("orderId") String orderId, @RequestParam(value = "openid", required = false) String openid) {
        if (StringUtils.isEmpty(orderId)) {
            return AjaxResult.error("订单ID不能为空");
        }
        
        // 获取订单信息
        OrderPrinter order = orderPrinterService.selectOrderPrinterByOrderId(orderId);
        if (order == null) {
            return AjaxResult.error("订单不存在");
        }
        
        // 如果提供了openid，验证订单是否属于该用户
        if (StringUtils.isNotEmpty(openid) && !openid.equals(order.getOpenid())) {
            return AjaxResult.error("无权查看该订单");
        }
        
        // 获取订单关联的打印任务
        List<OrderPrinterTask> tasks = orderPrinterService.getOrderTasks(orderId);

        // 计算订单总金额
        Double totalAmount = orderPrinterService.calculateOrderTotalAmount(orderId);
        order.setTotalAmount(totalAmount != null ? Math.round(totalAmount * 100) : 0L); // 转换为分

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("order", order);
        result.put("tasks", tasks);
        result.put("totalAmount", totalAmount); // 额外返回总金额字段

        return AjaxResult.success(result);
    }
    


    /**
     * 上传文件并创建/更新订单接口（合并接口）
     */
    @Log(title = "上传文件并创建订单", businessType = BusinessType.INSERT)
    @PostMapping("/user/uploadFile")
    public AjaxResult uploadFile(
            HttpServletRequest request,
            @RequestParam("file") MultipartFile file,
            UploadFileRequest uploadRequest) {
        log.info("打印机上传参数: {}", uploadRequest);

        // 自动获取 openid（优先级：前端传递 > ThreadLocal > JWT token）
        String finalOpenid = uploadRequest.getOpenid();
        if (StringUtils.isEmpty(finalOpenid)) {
            // 如果前端没有传递 openid，尝试从认证信息中获取
            finalOpenid = getCurrentUserOpenid(request);
            uploadRequest.setOpenid(finalOpenid); // 更新到请求对象中
            log.info("前端未传递 openid，从认证信息中获取到: {}", finalOpenid);
        } else {
            log.info("使用前端传递的 openid: {}", finalOpenid);
        }

        // 记录请求日志
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", uploadRequest.getOrderId());
        params.put("deviceId", uploadRequest.getDeviceId());
        params.put("deviceName", uploadRequest.getDeviceName());
        params.put("openid", finalOpenid); // 使用最终确定的 openid
        params.put("phone", uploadRequest.getPhone());
        params.put("copies", uploadRequest.getCopies());
        params.put("colorMode", uploadRequest.getColorMode());
        params.put("duplexMode", uploadRequest.getDuplexMode());
        params.put("paperType", uploadRequest.getPaperType());
        params.put("pageRange", uploadRequest.getPageRange());
        params.put("isLastUpload", uploadRequest.getIsLastUpload());
        params.put("isPhoto", uploadRequest.getIsPhoto());
        params.put("sizeSpec", uploadRequest.getSizeSpec());
        orderPrinterBusinessService.logRequestInfo(request, "上传文件并创建订单", params);

        // 参数验证
        if (StringUtils.isEmpty(uploadRequest.getDeviceId())) {
            log.error("参数验证失败: 设备ID不能为空");
            return AjaxResult.error("设备ID不能为空");
        }
        if (StringUtils.isEmpty(uploadRequest.getOrderId())) {
            log.error("参数验证失败: 订单ID不能为空");
            return AjaxResult.error("订单ID不能为空");
        }

        // 记录文件信息
        orderPrinterBusinessService.logFileInfo(file, null);

        try {
            // 检查订单是否存在，不存在则创建
            OrderPrinter existingOrder = orderPrinterService.selectOrderPrinterByOrderId(uploadRequest.getOrderId());
            if (existingOrder == null) {
                // 创建订单
                Map<String, Object> createResult = orderPrinterBusinessService.createOrder(
                    uploadRequest.getDeviceId(),
                    uploadRequest.getDeviceName(),
                    uploadRequest.getOpenid(),
                    uploadRequest.getPhone());
                // 使用前端传入的订单ID更新创建的订单
                OrderPrinter newOrder = orderPrinterService.selectOrderPrinterByOrderId((String) createResult.get("orderId"));
                if (newOrder != null && !uploadRequest.getOrderId().equals(newOrder.getOrderId())) {
                    // 删除自动生成的订单，使用前端传入的订单ID重新创建
                    orderPrinterService.deleteOrderPrinterByOrderId(newOrder.getOrderId());
                    newOrder.setOrderId(uploadRequest.getOrderId());
                    orderPrinterService.insertOrderPrinter(newOrder);
                }
                log.info("创建订单成功，订单ID: {}", uploadRequest.getOrderId());
            }

            // 调用业务服务上传文件
            Map<String, Object> result = orderPrinterBusinessService.uploadFile(
                    uploadRequest.getOrderId(),
                    file,
                    uploadRequest.getCopies(),
                    uploadRequest.getColorMode(),
                    uploadRequest.getDuplexMode(),
                    uploadRequest.getPaperType(),
                    uploadRequest.getPageRange(),
                    uploadRequest.getIsPhoto(),
                    uploadRequest.getSizeSpec());

            // 如果是最后一次上传，计算订单总金额
            if (uploadRequest.getIsLastUpload()) {
                Double totalAmount = orderPrinterService.calculateOrderTotalAmount(uploadRequest.getOrderId());
                OrderPrinter order = orderPrinterService.selectOrderPrinterByOrderId(uploadRequest.getOrderId());
                order.setTotalAmount(totalAmount != null ? Math.round(totalAmount) : 0L); // 元为单位
                orderPrinterService.updateOrderPrinter(order);
                result.put("totalAmount", totalAmount);
                result.put("isOrderComplete", true);
                log.info("订单完成，总金额: {} 元", totalAmount);
            }

            log.info("上传文件成功，任务ID: {}, 价格: {}", result.get("taskId"), result.get("taskPrice"));
            return AjaxResult.success("上传文件成功", result);
        } catch (Exception e) {
            log.error("上传文件失败: {}", e.getMessage(), e);
            return AjaxResult.error("上传文件失败: " + e.getMessage());
        }
    }

    /**
     * 更新订单信息（包括订单明细更新）- 使用DTO对象
     */
    @Log(title = "更新订单信息", businessType = BusinessType.UPDATE)
    @PostMapping("/user/updateOrderWithDto")
    public AjaxResult updateOrderWithDto(@RequestBody OrderUpdateRequest request, HttpServletRequest httpRequest) {
        // 验证请求对象
        if (request == null) {
            log.error("请求体为空或JSON格式错误");
            return AjaxResult.error("请求体不能为空，请检查JSON格式");
        }

        // 详细记录接收到的参数
        log.info("=== 接收到的请求参数详情 ===");
        log.info("订单ID: {}", request.getOrderId());
        log.info("设备名称: {}", request.getDeviceName());
        log.info("手机号: {}", request.getPhone());
        log.info("优惠券码: {}", request.getVoucherCode());
        log.info("备注: {}", request.getRemark());
        log.info("订单状态: {}", request.getOrderStatus());
        log.info("任务更新列表: {}", request.getTaskUpdates());
        log.info("================================");
        // 记录请求日志
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", request.getOrderId());
        params.put("deviceName", request.getDeviceName());
        params.put("phone", request.getPhone());
        params.put("voucherCode", request.getVoucherCode());
        params.put("remark", request.getRemark());
        params.put("orderStatus", request.getOrderStatus()); // 添加订单状态参数
        params.put("taskUpdatesCount", request.getTaskUpdates() != null ? request.getTaskUpdates().size() : 0);
        orderPrinterBusinessService.logRequestInfo(httpRequest, "更新订单信息(DTO)", params);

        // 验证订单是否存在
        OrderPrinter existingOrder = orderPrinterService.selectOrderPrinterByOrderId(request.getOrderId());
        if (existingOrder == null) {
            log.error("订单不存在: {}", request.getOrderId());
            return AjaxResult.error("订单不存在");
        }

        // 获取当前用户openid进行权限验证
        String currentOpenid = getCurrentUserOpenid(httpRequest);
        if (StringUtils.isNotEmpty(currentOpenid) && !currentOpenid.equals(existingOrder.getOpenid())) {
            log.error("无权限更新订单: 当前用户openid={}, 订单openid={}", currentOpenid, existingOrder.getOpenid());
            return AjaxResult.error("无权限更新该订单");
        }

        // 验证订单状态是否允许更新
        Integer orderStatus = existingOrder.getOrderStatus();
        if (orderStatus != null && (orderStatus == 2 || orderStatus == 3)) {
            log.error("订单状态不允许更新: orderId={}, status={}", request.getOrderId(), orderStatus);
            return AjaxResult.error("已取消或已退款的订单不允许更新");
        }

        try {
            // 更新订单基本信息
            Map<String, Object> updateResult = updateOrderBasicInfoFromDto(existingOrder, request);

            // 更新订单明细（打印任务）
            Map<String, Object> taskUpdateResult = updateOrderTasksFromDto(request.getOrderId(), request.getTaskUpdates());

            // 重新计算订单总金额
            Double totalAmount = orderPrinterService.calculateOrderTotalAmount(request.getOrderId());
            if (totalAmount != null) {
                existingOrder.setTotalAmount(Math.round(totalAmount));
                orderPrinterService.updateOrderPrinter(existingOrder);
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("orderId", request.getOrderId());
            result.put("orderUpdate", updateResult);
            result.put("taskUpdate", taskUpdateResult);
            result.put("totalAmount", totalAmount);

            log.info("订单更新成功: orderId={}, totalAmount={}", request.getOrderId(), totalAmount);
            return AjaxResult.success("订单更新成功", result);

        } catch (Exception e) {
            log.error("订单更新失败: orderId={}, error={}", request.getOrderId(), e.getMessage(), e);
            return AjaxResult.error("订单更新失败: " + e.getMessage());
        }
    }

    /**
     * 更新订单信息（包括订单明细更新）- 使用Map参数（兼容旧版本）
     */
    @Log(title = "更新订单信息", businessType = BusinessType.UPDATE)
    @PostMapping("/updateOrder")
    public AjaxResult updateOrder(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        // 记录请求日志
        orderPrinterBusinessService.logRequestInfo(request, "更新订单信息", params);

        // 获取订单ID
        String orderId = (String) params.get("orderId");
        if (StringUtils.isEmpty(orderId)) {
            log.error("参数验证失败: 订单ID不能为空");
            return AjaxResult.error("订单ID不能为空");
        }

        // 验证订单是否存在
        OrderPrinter existingOrder = orderPrinterService.selectOrderPrinterByOrderId(orderId);
        if (existingOrder == null) {
            log.error("订单不存在: {}", orderId);
            return AjaxResult.error("订单不存在");
        }

        // 获取当前用户openid进行权限验证
        String currentOpenid = getCurrentUserOpenid(request);
        if (StringUtils.isNotEmpty(currentOpenid) && !currentOpenid.equals(existingOrder.getOpenid())) {
            log.error("无权限更新订单: 当前用户openid={}, 订单openid={}", currentOpenid, existingOrder.getOpenid());
            return AjaxResult.error("无权限更新该订单");
        }

        // 验证订单状态是否允许更新
        Integer orderStatus = existingOrder.getOrderStatus();
        if (orderStatus != null && (orderStatus == 2 || orderStatus == 3)) {
            log.error("订单状态不允许更新: orderId={}, status={}", orderId, orderStatus);
            return AjaxResult.error("已取消或已退款的订单不允许更新");
        }

        try {
            // 更新订单基本信息
            Map<String, Object> updateResult = updateOrderBasicInfo(existingOrder, params);

            // 更新订单明细（打印任务）
            Map<String, Object> taskUpdateResult = updateOrderTasks(orderId, params);

            // 重新计算订单总金额
            Double totalAmount = orderPrinterService.calculateOrderTotalAmount(orderId);
            if (totalAmount != null) {
                existingOrder.setTotalAmount(Math.round(totalAmount));
                orderPrinterService.updateOrderPrinter(existingOrder);
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("orderId", orderId);
            result.put("orderUpdate", updateResult);
            result.put("taskUpdate", taskUpdateResult);
            result.put("totalAmount", totalAmount);

            log.info("订单更新成功: orderId={}, totalAmount={}", orderId, totalAmount);
            return AjaxResult.success("订单更新成功", result);

        } catch (Exception e) {
            log.error("订单更新失败: orderId={}, error={}", orderId, e.getMessage(), e);
            return AjaxResult.error("订单更新失败: " + e.getMessage());
        }
    }

    /**
     * 更新订单基本信息
     */
    private Map<String, Object> updateOrderBasicInfo(OrderPrinter existingOrder, Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        boolean hasUpdate = false;

        // 可更新的字段列表
        String[] updatableFields = {"deviceName", "phone", "voucherCode", "remark"};

        for (String field : updatableFields) {
            if (params.containsKey(field)) {
                Object newValue = params.get(field);
                switch (field) {
                    case "deviceName":
                        if (newValue != null && !newValue.equals(existingOrder.getDeviceName())) {
                            existingOrder.setDeviceName((String) newValue);
                            hasUpdate = true;
                            result.put("deviceName", "已更新");
                        }
                        break;
                    case "phone":
                        if (newValue != null && !newValue.equals(existingOrder.getPhone())) {
                            existingOrder.setPhone((String) newValue);
                            hasUpdate = true;
                            result.put("phone", "已更新");
                        }
                        break;
                    case "voucherCode":
                        if (newValue != null && !newValue.equals(existingOrder.getVoucherCode())) {
                            existingOrder.setVoucherCode((String) newValue);
                            hasUpdate = true;
                            result.put("voucherCode", "已更新");
                        }
                        break;
                    case "remark":
                        if (newValue != null && !newValue.equals(existingOrder.getRemark())) {
                            existingOrder.setRemark((String) newValue);
                            hasUpdate = true;
                            result.put("remark", "已更新");
                        }
                        break;
                }
            }
        }

        // 如果有更新，保存到数据库
        if (hasUpdate) {
            int updateCount = orderPrinterService.updateOrderPrinter(existingOrder);
            result.put("updateCount", updateCount);
            log.info("订单基本信息更新成功: orderId={}, updateCount={}", existingOrder.getOrderId(), updateCount);
        } else {
            result.put("message", "无需更新");
        }

        return result;
    }

    /**
     * 更新订单明细（打印任务）
     */
    private Map<String, Object> updateOrderTasks(String orderId, Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();

        // 获取任务更新列表
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> taskUpdates = (List<Map<String, Object>>) params.get("taskUpdates");

        if (taskUpdates == null || taskUpdates.isEmpty()) {
            result.put("message", "无任务更新");
            return result;
        }

        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();

        for (Map<String, Object> taskUpdate : taskUpdates) {
            try {
                String taskId = (String) taskUpdate.get("taskId");
                if (StringUtils.isEmpty(taskId)) {
                    errors.add("任务ID不能为空");
                    failCount++;
                    continue;
                }

                // 验证任务是否属于该订单
                OrderPrinterTask existingTask = orderPrinterService.getOrderTasks(orderId)
                    .stream()
                    .filter(task -> taskId.equals(task.getTaskId()))
                    .findFirst()
                    .orElse(null);

                if (existingTask == null) {
                    errors.add("任务不存在或不属于该订单: " + taskId);
                    failCount++;
                    continue;
                }

                // 验证任务状态是否允许更新
                if (existingTask.getPrintStatus() != null && existingTask.getPrintStatus() == 2) {
                    errors.add("已完成的任务不允许更新: " + taskId);
                    failCount++;
                    continue;
                }

                // 更新任务信息
                boolean taskUpdated = updateSingleTask(existingTask, taskUpdate);
                if (taskUpdated) {
                    successCount++;
                } else {
                    failCount++;
                    errors.add("任务更新失败: " + taskId);
                }

            } catch (Exception e) {
                failCount++;
                errors.add("任务更新异常: " + e.getMessage());
                log.error("更新任务失败: {}", e.getMessage(), e);
            }
        }

        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);

        return result;
    }

    /**
     * 更新单个打印任务
     */
    private boolean updateSingleTask(OrderPrinterTask existingTask, Map<String, Object> taskUpdate) {
        boolean hasUpdate = false;

        // 可更新的任务字段
        if (taskUpdate.containsKey("copies")) {
            Integer newCopies = (Integer) taskUpdate.get("copies");
            if (newCopies != null && newCopies > 0 && !newCopies.equals(existingTask.getCopies())) {
                existingTask.setCopies(newCopies);
                hasUpdate = true;
            }
        }

        if (taskUpdate.containsKey("colorMode")) {
            Integer newColorMode = (Integer) taskUpdate.get("colorMode");
            if (newColorMode != null && (newColorMode == 0 || newColorMode == 1) && !newColorMode.equals(existingTask.getColorMode())) {
                existingTask.setColorMode(newColorMode);
                hasUpdate = true;
            }
        }

        if (taskUpdate.containsKey("duplexMode")) {
            Integer newDuplexMode = (Integer) taskUpdate.get("duplexMode");
            if (newDuplexMode != null && (newDuplexMode == 0 || newDuplexMode == 1) && !newDuplexMode.equals(existingTask.getDuplexMode())) {
                existingTask.setDuplexMode(newDuplexMode);
                hasUpdate = true;
            }
        }

        if (taskUpdate.containsKey("paperType")) {
            Integer newPaperType = (Integer) taskUpdate.get("paperType");
            if (newPaperType != null && newPaperType >= 1 && newPaperType <= 3 && !newPaperType.equals(existingTask.getPaperType())) {
                existingTask.setPaperType(newPaperType);
                hasUpdate = true;
            }
        }

        if (taskUpdate.containsKey("pageRange")) {
            String newPageRange = (String) taskUpdate.get("pageRange");
            if (StringUtils.isNotEmpty(newPageRange) && !newPageRange.equals(existingTask.getPageRange())) {
                existingTask.setPageRange(newPageRange);
                hasUpdate = true;
            }
        }

        // 如果有更新，重新计算价格并保存
        if (hasUpdate) {
            try {
                // 重新计算任务价格
                int pageCount = orderPrinterService.parsePageRange(existingTask.getPageRange());
                Double newPrice = orderPrinterService.calculatePrintPrice(
                    pageCount,
                    existingTask.getCopies(),
                    existingTask.getColorMode(),
                    existingTask.getDuplexMode(),
                    existingTask.getPaperType(),
                    existingTask.getDeviceId()
                );
                existingTask.setTaskPrice(newPrice);

                // 更新任务到数据库
                // 注意：这里需要在Service层添加更新任务的方法
                return updateTaskInDatabase(existingTask);

            } catch (Exception e) {
                log.error("更新任务失败: taskId={}, error={}", existingTask.getTaskId(), e.getMessage(), e);
                return false;
            }
        }

        return true; // 无需更新也算成功
    }

    /**
     * 更新任务到数据库
     */
    private boolean updateTaskInDatabase(OrderPrinterTask task) {
        try {
            int result = orderPrinterService.updateTask(task);
            return result > 0;
        } catch (Exception e) {
            log.error("更新任务到数据库失败: taskId={}, error={}", task.getTaskId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从DTO更新订单基本信息
     */
    private Map<String, Object> updateOrderBasicInfoFromDto(OrderPrinter existingOrder, OrderUpdateRequest request) {
        Map<String, Object> result = new HashMap<>();
        boolean hasUpdate = false;

        // 更新设备名称
        if (StringUtils.isNotEmpty(request.getDeviceName()) && !request.getDeviceName().equals(existingOrder.getDeviceName())) {
            existingOrder.setDeviceName(request.getDeviceName());
            hasUpdate = true;
            result.put("deviceName", "已更新");
        }

        // 更新手机号
        if (StringUtils.isNotEmpty(request.getPhone()) && !request.getPhone().equals(existingOrder.getPhone())) {
            existingOrder.setPhone(request.getPhone());
            hasUpdate = true;
            result.put("phone", "已更新");
        }

        // 更新优惠券码
        if (StringUtils.isNotEmpty(request.getVoucherCode()) && !request.getVoucherCode().equals(existingOrder.getVoucherCode())) {
            existingOrder.setVoucherCode(request.getVoucherCode());
            hasUpdate = true;
            result.put("voucherCode", "已更新");
        }

        // 更新备注
        if (StringUtils.isNotEmpty(request.getRemark()) && !request.getRemark().equals(existingOrder.getRemark())) {
            existingOrder.setRemark(request.getRemark());
            hasUpdate = true;
            result.put("remark", "已更新");
        }

        // 更新订单状态
        if (request.getOrderStatus() != null && !request.getOrderStatus().equals(existingOrder.getOrderStatus())) {
            // 验证订单状态的合法性
            if (request.getOrderStatus() >= 0 && request.getOrderStatus() <= 4) {
                existingOrder.setOrderStatus(request.getOrderStatus());
                hasUpdate = true;
                result.put("orderStatus", "已更新");
                log.info("订单状态更新: orderId={}, 原状态={}, 新状态={}",
                        existingOrder.getOrderId(), existingOrder.getOrderStatus(), request.getOrderStatus());
            } else {
                log.warn("无效的订单状态: {}, 允许的状态值: 0-未支付, 1-已支付, 2-已取消, 3-已退款, 4-已完成", request.getOrderStatus());
                result.put("orderStatusError", "无效的订单状态值");
            }
        }

        // 如果有更新，保存到数据库
        if (hasUpdate) {
            int updateCount = orderPrinterService.updateOrderPrinter(existingOrder);
            result.put("updateCount", updateCount);
            log.info("订单基本信息更新成功: orderId={}, updateCount={}", existingOrder.getOrderId(), updateCount);
        } else {
            result.put("message", "无需更新");
        }

        return result;
    }

    /**
     * 从DTO更新订单明细（打印任务）
     */
    private Map<String, Object> updateOrderTasksFromDto(String orderId, List<OrderUpdateRequest.TaskUpdateInfo> taskUpdates) {
        Map<String, Object> result = new HashMap<>();

        if (taskUpdates == null || taskUpdates.isEmpty()) {
            result.put("message", "无任务更新");
            return result;
        }

        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();

        for (OrderUpdateRequest.TaskUpdateInfo taskUpdate : taskUpdates) {
            try {
                String taskId = taskUpdate.getTaskId();
                if (StringUtils.isEmpty(taskId)) {
                    errors.add("任务ID不能为空");
                    failCount++;
                    continue;
                }

                // 验证任务是否属于该订单
                OrderPrinterTask existingTask = orderPrinterService.getOrderTasks(orderId)
                    .stream()
                    .filter(task -> taskId.equals(task.getTaskId()))
                    .findFirst()
                    .orElse(null);

                if (existingTask == null) {
                    errors.add("任务不存在或不属于该订单: " + taskId);
                    failCount++;
                    continue;
                }

                // 验证任务状态是否允许更新
                if (existingTask.getPrintStatus() != null && existingTask.getPrintStatus() == 2) {
                    errors.add("已完成的任务不允许更新: " + taskId);
                    failCount++;
                    continue;
                }

                // 更新任务信息
                boolean taskUpdated = updateSingleTaskFromDto(existingTask, taskUpdate);
                if (taskUpdated) {
                    successCount++;
                } else {
                    failCount++;
                    errors.add("任务更新失败: " + taskId);
                }

            } catch (Exception e) {
                failCount++;
                errors.add("任务更新异常: " + e.getMessage());
                log.error("更新任务失败: {}", e.getMessage(), e);
            }
        }

        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);

        return result;
    }

    /**
     * 从DTO更新单个打印任务
     */
    private boolean updateSingleTaskFromDto(OrderPrinterTask existingTask, OrderUpdateRequest.TaskUpdateInfo taskUpdate) {
        boolean hasUpdate = false;

        // 更新打印份数
        if (taskUpdate.getCopies() != null && taskUpdate.getCopies() > 0 && !taskUpdate.getCopies().equals(existingTask.getCopies())) {
            existingTask.setCopies(taskUpdate.getCopies());
            hasUpdate = true;
        }

        // 更新颜色模式
        if (taskUpdate.getColorMode() != null && (taskUpdate.getColorMode() == 0 || taskUpdate.getColorMode() == 1) && !taskUpdate.getColorMode().equals(existingTask.getColorMode())) {
            existingTask.setColorMode(taskUpdate.getColorMode());
            hasUpdate = true;
        }

        // 更新双面模式
        if (taskUpdate.getDuplexMode() != null && (taskUpdate.getDuplexMode() == 0 || taskUpdate.getDuplexMode() == 1) && !taskUpdate.getDuplexMode().equals(existingTask.getDuplexMode())) {
            existingTask.setDuplexMode(taskUpdate.getDuplexMode());
            hasUpdate = true;
        }

        // 更新纸张类型
        if (taskUpdate.getPaperType() != null && taskUpdate.getPaperType() >= 1 && taskUpdate.getPaperType() <= 3 && !taskUpdate.getPaperType().equals(existingTask.getPaperType())) {
            existingTask.setPaperType(taskUpdate.getPaperType());
            hasUpdate = true;
        }

        // 更新页码范围
        if (StringUtils.isNotEmpty(taskUpdate.getPageRange()) && !taskUpdate.getPageRange().equals(existingTask.getPageRange())) {
            existingTask.setPageRange(taskUpdate.getPageRange());
            hasUpdate = true;
        }

        // 如果有更新，重新计算价格并保存
        if (hasUpdate) {
            try {
                // 重新计算任务价格
                int pageCount = orderPrinterService.parsePageRange(existingTask.getPageRange());
                Double newPrice = orderPrinterService.calculatePrintPrice(
                    pageCount,
                    existingTask.getCopies(),
                    existingTask.getColorMode(),
                    existingTask.getDuplexMode(),
                    existingTask.getPaperType(),
                    existingTask.getDeviceId()
                );
                existingTask.setTaskPrice(newPrice);

                // 更新任务到数据库
                return updateTaskInDatabase(existingTask);

            } catch (Exception e) {
                log.error("更新任务失败: taskId={}, error={}", existingTask.getTaskId(), e.getMessage(), e);
                return false;
            }
        }

        return true; // 无需更新也算成功
    }

    /**
     * 从请求中获取当前用户的openid
     * 优先级：1. 从ThreadLocal中获取 2. 从JWT token中获取 3. 返回null
     */
    private String getCurrentUserOpenid(HttpServletRequest request) {
        try {
            // 方法1：尝试从ThreadLocal中获取（如果经过了认证拦截器）
            WxappLoginUser currentUser = UserContext.getCurrentUser();
            if (currentUser != null && StringUtils.isNotEmpty(currentUser.getOpenid())) {
                log.info("从ThreadLocal获取到openid: {}", currentUser.getOpenid());
                return currentUser.getOpenid();
            }

            // 方法2：尝试从JWT token中获取
            WxappLoginUser loginUser = wxAppAuthService.getClientUser(request);
            if (loginUser != null && StringUtils.isNotEmpty(loginUser.getOpenid())) {
                log.info("从JWT token获取到openid: {}", loginUser.getOpenid());
                return loginUser.getOpenid();
            }
        } catch (Exception e) {
            log.warn("获取用户openid失败: {}", e.getMessage());
        }
        return null;
    }


}