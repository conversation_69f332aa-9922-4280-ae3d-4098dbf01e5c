package com.yunchuang.wxapp.service.client.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.device.mapper.IdPhotoPrintParamsMapper;
import com.ruoyi.device.domain.IdPhotoPrintParams;
import com.yunchuang.wxapp.service.client.ICIdPhotoPrintParamsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 证件照打印参数 Service 实现类
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Service
@Transactional
public class CIdPhotoPrintParamsServiceImpl extends ServiceImpl<IdPhotoPrintParamsMapper, IdPhotoPrintParams> implements ICIdPhotoPrintParamsService {

    @Resource
    private IdPhotoPrintParamsMapper idPhotoPrintParamsMapper;

    /**
     * 获取启用的证件照打印参数列表
     *
     * @return 证件照打印参数列表
     */
    @Override
    public List<IdPhotoPrintParams> getEnabledIdPhotoPrintParams() {
        LambdaQueryWrapper<IdPhotoPrintParams> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdPhotoPrintParams::getStatus, IdPhotoPrintParams.STATUS_ENABLED); // 0为启用
        queryWrapper.orderByAsc(IdPhotoPrintParams::getSortOrder);
        return idPhotoPrintParamsMapper.selectList(queryWrapper);
    }

    /**
     * 根据类型获取证件照打印参数
     *
     * @param type 类型
     * @return 证件照打印参数
     */
    @Override
    public IdPhotoPrintParams getIdPhotoPrintParamsByType(Integer type) {
        LambdaQueryWrapper<IdPhotoPrintParams> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdPhotoPrintParams::getType, type);
        queryWrapper.eq(IdPhotoPrintParams::getStatus, IdPhotoPrintParams.STATUS_ENABLED); // 0为启用
        return idPhotoPrintParamsMapper.selectOne(queryWrapper);
    }

    /**
     * 获取启用状态的证件照打印参数并按类型分组
     *
     * @return 按类型分组的证件照打印参数
     */
    @Override
    public Map<String, List<IdPhotoPrintParams>> getEnabledParamsGroupByType() {
        // 查询启用状态的参数
        LambdaQueryWrapper<IdPhotoPrintParams> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdPhotoPrintParams::getStatus, IdPhotoPrintParams.STATUS_ENABLED); // 0为启用
        queryWrapper.orderByAsc(IdPhotoPrintParams::getSortOrder);
        List<IdPhotoPrintParams> enabledParams = idPhotoPrintParamsMapper.selectList(queryWrapper);

        // 按类型分组
        Map<String, List<IdPhotoPrintParams>> groupedParams = new HashMap<>();

        // 按类型分组并排序
        Map<Integer, List<IdPhotoPrintParams>> typeGrouped = enabledParams.stream()
                .collect(Collectors.groupingBy(IdPhotoPrintParams::getType));

        // 转换为字符串键值并添加到结果Map
        typeGrouped.forEach((type, params) -> {
            String typeName = getTypeName(type);
            // 按排序权重排序
            params.sort((a, b) -> {
                int sortA = a.getSortOrder() != null ? a.getSortOrder() : 0;
                int sortB = b.getSortOrder() != null ? b.getSortOrder() : 0;
                return Integer.compare(sortA, sortB);
            });
            groupedParams.put(typeName, params);
        });

        return groupedParams;
    }

    /**
     * 根据类型值获取类型名称
     *
     * @param type 类型值
     * @return 类型名称
     */
    private String getTypeName(Integer type) {
        if (type == null) {
            return "unknown";
        }
        switch (type) {
            case 0:
                return "common";
            case 1:
                return "identity";
            case 2:
                return "education";
            case 3:
                return "qualification";
            case 4:
                return "visa";
            default:
                return "unknown";
        }
    }

}
