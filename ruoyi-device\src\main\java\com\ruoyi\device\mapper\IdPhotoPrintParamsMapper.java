package com.ruoyi.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.device.domain.IdPhotoPrintParams;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 证件照打印参数Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Mapper
public interface IdPhotoPrintParamsMapper extends BaseMapper<IdPhotoPrintParams> {

    /**
     * 查询证件照打印参数
     * 
     * @param id 证件照打印参数主键
     * @return 证件照打印参数
     */
    public IdPhotoPrintParams selectIdPhotoPrintParamsById(Long id);

    /**
     * 查询证件照打印参数列表
     * 
     * @param idPhotoPrintParams 证件照打印参数
     * @return 证件照打印参数集合
     */
    public List<IdPhotoPrintParams> selectIdPhotoPrintParamsList(IdPhotoPrintParams idPhotoPrintParams);

    /**
     * 新增证件照打印参数
     * 
     * @param idPhotoPrintParams 证件照打印参数
     * @return 结果
     */
    public int insertIdPhotoPrintParams(IdPhotoPrintParams idPhotoPrintParams);

    /**
     * 修改证件照打印参数
     * 
     * @param idPhotoPrintParams 证件照打印参数
     * @return 结果
     */
    public int updateIdPhotoPrintParams(IdPhotoPrintParams idPhotoPrintParams);

    /**
     * 删除证件照打印参数
     * 
     * @param id 证件照打印参数主键
     * @return 结果
     */
    public int deleteIdPhotoPrintParamsById(Long id);

    /**
     * 批量删除证件照打印参数
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIdPhotoPrintParamsByIds(Long[] ids);

    /**
     * 根据类型查询证件照打印参数
     *
     * @param type 类型
     * @return 证件照打印参数
     */
    public IdPhotoPrintParams selectIdPhotoPrintParamsByType(Integer type);

}
