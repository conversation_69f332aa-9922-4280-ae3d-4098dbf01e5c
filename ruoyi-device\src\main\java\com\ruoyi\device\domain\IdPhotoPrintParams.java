package com.ruoyi.device.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 证件照打印参数对象 id_photo_print_params
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("id_photo_print_params")
public class IdPhotoPrintParams extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 类型常量 */
    public static final Integer TYPE_COMMON = 0;        // 通用
    public static final Integer TYPE_IDENTITY = 1;      // 证件
    public static final Integer TYPE_EDUCATION = 2;     // 教育
    public static final Integer TYPE_QUALIFICATION = 3; // 从业资格
    public static final Integer TYPE_VISA = 4;          // 签证

    /** 状态常量 */
    public static final Integer STATUS_ENABLED = 0;     // 启用
    public static final Integer STATUS_DISABLED = 1;    // 禁用

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private Integer type;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 宽度(mm)
     */
    @Excel(name = "宽度(mm)")
    private Integer widthMm;

    /**
     * 高度(mm)
     */
    @Excel(name = "高度(mm)")
    private Integer heightMm;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private Integer status;

    /**
     * 排序权重
     */
    @Excel(name = "排序权重")
    private Integer sortOrder;

}
