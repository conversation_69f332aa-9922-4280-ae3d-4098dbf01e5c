package com.ruoyi.web.controller.device;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.device.domain.IdPhotoPrintParams;
import com.ruoyi.device.service.IIdPhotoPrintParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 证件照参数Controller
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@RestController
@RequestMapping("/device/idPhotoParams")
public class IdPhotoPrintParamsController extends BaseController
{
    @Autowired
    private IIdPhotoPrintParamsService idPhotoPrintParamsService;

    /**
     * 查询证件照参数列表
     */
    @PreAuthorize("@ss.hasPermi('device:idPhotoParams:list')")
    @GetMapping("/list")
    public TableDataInfo list(IdPhotoPrintParams idPhotoPrintParams)
    {
        startPage();
        List<IdPhotoPrintParams> list = idPhotoPrintParamsService.selectIdPhotoPrintParamsList(idPhotoPrintParams);
        return getDataTable(list);
    }

    /**
     * 导出证件照参数列表
     */
    @PreAuthorize("@ss.hasPermi('device:idPhotoParams:export')")
    @Log(title = "证件照参数", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IdPhotoPrintParams idPhotoPrintParams)
    {
        List<IdPhotoPrintParams> list = idPhotoPrintParamsService.selectIdPhotoPrintParamsList(idPhotoPrintParams);
        ExcelUtil<IdPhotoPrintParams> util = new ExcelUtil<IdPhotoPrintParams>(IdPhotoPrintParams.class);
        util.exportExcel(response, list, "证件照参数数据");
    }

    /**
     * 获取证件照参数详细信息
     */
    @PreAuthorize("@ss.hasPermi('device:idPhotoParams:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(idPhotoPrintParamsService.selectIdPhotoPrintParamsById(id));
    }

    /**
     * 新增证件照参数
     */
    @PreAuthorize("@ss.hasPermi('device:idPhotoParams:add')")
    @Log(title = "证件照参数", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IdPhotoPrintParams idPhotoPrintParams)
    {
        // 设置默认值
        if (idPhotoPrintParams.getStatus() == null) {
            idPhotoPrintParams.setStatus(IdPhotoPrintParams.STATUS_ENABLED); // 默认启用
        }
        if (idPhotoPrintParams.getSortOrder() == null) {
            idPhotoPrintParams.setSortOrder(0); // 默认排序
        }
        
        return toAjax(idPhotoPrintParamsService.insertIdPhotoPrintParams(idPhotoPrintParams));
    }

    /**
     * 修改证件照参数
     */
    @PreAuthorize("@ss.hasPermi('device:idPhotoParams:edit')")
    @Log(title = "证件照参数", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IdPhotoPrintParams idPhotoPrintParams)
    {
        return toAjax(idPhotoPrintParamsService.updateIdPhotoPrintParams(idPhotoPrintParams));
    }

    /**
     * 删除证件照参数
     */
    @PreAuthorize("@ss.hasPermi('device:idPhotoParams:remove')")
    @Log(title = "证件照参数", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(idPhotoPrintParamsService.deleteIdPhotoPrintParamsByIds(ids));
    }

    /**
     * 获取证件照参数类型字典
     */
    @GetMapping("/typeDict")
    public AjaxResult getTypeDict()
    {
        // 返回类型字典数据
        return AjaxResult.success()
                .put("0", "通用")
                .put("1", "证件")
                .put("2", "教育")
                .put("3", "从业资格")
                .put("4", "签证");
    }

    /**
     * 获取状态字典
     */
    @GetMapping("/statusDict")
    public AjaxResult getStatusDict()
    {
        // 返回状态字典数据
        return AjaxResult.success()
                .put("0", "启用")
                .put("1", "禁用");
    }

    /**
     * 根据类型查询证件照参数
     */
    @GetMapping("/getByType/{type}")
    public AjaxResult getByType(@PathVariable("type") Integer type)
    {
        return success(idPhotoPrintParamsService.selectIdPhotoPrintParamsByType(type));
    }

    /**
     * 获取启用状态的证件照参数并按类型分组
     */
    @GetMapping("/groupByType")
    public AjaxResult getGroupByType()
    {
        return success(idPhotoPrintParamsService.selectEnabledParamsGroupByType());
    }

    /**
     * 批量更新状态
     */
    @PreAuthorize("@ss.hasPermi('device:idPhotoParams:edit')")
    @Log(title = "证件照参数", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStatus")
    public AjaxResult updateStatus(@RequestBody List<Long> ids, @RequestParam Integer status)
    {
        int successCount = 0;
        for (Long id : ids) {
            IdPhotoPrintParams params = new IdPhotoPrintParams();
            params.setId(id);
            params.setStatus(status);
            successCount += idPhotoPrintParamsService.updateIdPhotoPrintParams(params);
        }
        return success("成功更新" + successCount + "条记录");
    }

    /**
     * 批量更新排序
     */
    @PreAuthorize("@ss.hasPermi('device:idPhotoParams:edit')")
    @Log(title = "证件照参数", businessType = BusinessType.UPDATE)
    @PutMapping("/updateSort")
    public AjaxResult updateSort(@RequestBody List<IdPhotoPrintParams> paramsList)
    {
        int successCount = 0;
        for (IdPhotoPrintParams params : paramsList) {
            if (params.getId() != null && params.getSortOrder() != null) {
                IdPhotoPrintParams updateParams = new IdPhotoPrintParams();
                updateParams.setId(params.getId());
                updateParams.setSortOrder(params.getSortOrder());
                successCount += idPhotoPrintParamsService.updateIdPhotoPrintParams(updateParams);
            }
        }
        return success("成功更新" + successCount + "条记录的排序");
    }
}
