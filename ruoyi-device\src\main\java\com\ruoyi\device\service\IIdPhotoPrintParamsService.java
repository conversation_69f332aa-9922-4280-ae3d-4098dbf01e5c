package com.ruoyi.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.device.domain.IdPhotoPrintParams;

import java.util.List;
import java.util.Map;

/**
 * 证件照打印参数Service接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface IIdPhotoPrintParamsService extends IService<IdPhotoPrintParams> {

    /**
     * 查询证件照打印参数
     *
     * @param id 证件照打印参数主键
     * @return 证件照打印参数
     */
    public IdPhotoPrintParams selectIdPhotoPrintParamsById(Long id);

    /**
     * 查询证件照打印参数列表
     *
     * @param idPhotoPrintParams 证件照打印参数
     * @return 证件照打印参数集合
     */
    public List<IdPhotoPrintParams> selectIdPhotoPrintParamsList(IdPhotoPrintParams idPhotoPrintParams);

    /**
     * 查询启用状态的证件照打印参数并按类型分组
     *
     * @return 按类型分组的证件照打印参数
     */
    public Map<String, List<IdPhotoPrintParams>> selectEnabledParamsGroupByType();

    /**
     * 新增证件照打印参数
     * 
     * @param idPhotoPrintParams 证件照打印参数
     * @return 结果
     */
    public int insertIdPhotoPrintParams(IdPhotoPrintParams idPhotoPrintParams);

    /**
     * 修改证件照打印参数
     * 
     * @param idPhotoPrintParams 证件照打印参数
     * @return 结果
     */
    public int updateIdPhotoPrintParams(IdPhotoPrintParams idPhotoPrintParams);

    /**
     * 批量删除证件照打印参数
     * 
     * @param ids 需要删除的证件照打印参数主键集合
     * @return 结果
     */
    public int deleteIdPhotoPrintParamsByIds(Long[] ids);

    /**
     * 删除证件照打印参数信息
     * 
     * @param id 证件照打印参数主键
     * @return 结果
     */
    public int deleteIdPhotoPrintParamsById(Long id);

    /**
     * 根据类型查询证件照打印参数
     *
     * @param type 类型
     * @return 证件照打印参数
     */
    public IdPhotoPrintParams selectIdPhotoPrintParamsByType(Integer type);

}
