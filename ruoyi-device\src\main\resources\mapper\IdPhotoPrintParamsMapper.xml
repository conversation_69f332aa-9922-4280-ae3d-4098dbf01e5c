<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.device.mapper.IdPhotoPrintParamsMapper">
    
    <resultMap type="IdPhotoPrintParams" id="IdPhotoPrintParamsResult">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
        <result property="widthMm" column="width_mm"/>
        <result property="heightMm" column="height_mm"/>
        <result property="status" column="status"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectIdPhotoPrintParamsVo">
        select id, type, name, width_mm, height_mm, status, sort_order, create_by, create_time, update_by, update_time, remark
        from id_photo_print_params
    </sql>

    <select id="selectIdPhotoPrintParamsList" parameterType="IdPhotoPrintParams" resultMap="IdPhotoPrintParamsResult">
        <include refid="selectIdPhotoPrintParamsVo"/>
        <where>  
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="widthMm != null "> and width_mm = #{widthMm}</if>
            <if test="heightMm != null "> and height_mm = #{heightMm}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
        </where>
        order by sort_order asc
    </select>
    
    <select id="selectIdPhotoPrintParamsById" parameterType="Long" resultMap="IdPhotoPrintParamsResult">
        <include refid="selectIdPhotoPrintParamsVo"/>
        where id = #{id}
    </select>

    <select id="selectIdPhotoPrintParamsByType" parameterType="Integer" resultMap="IdPhotoPrintParamsResult">
        <include refid="selectIdPhotoPrintParamsVo"/>
        where type = #{type} and status = 0
    </select>
        
    <insert id="insertIdPhotoPrintParams" parameterType="IdPhotoPrintParams" useGeneratedKeys="true" keyProperty="id">
        insert into id_photo_print_params
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="name != null">name,</if>
            <if test="widthMm != null">width_mm,</if>
            <if test="heightMm != null">height_mm,</if>
            <if test="status != null">status,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="name != null">#{name},</if>
            <if test="widthMm != null">#{widthMm},</if>
            <if test="heightMm != null">#{heightMm},</if>
            <if test="status != null">#{status},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateIdPhotoPrintParams" parameterType="IdPhotoPrintParams">
        update id_photo_print_params
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="name != null">name = #{name},</if>
            <if test="widthMm != null">width_mm = #{widthMm},</if>
            <if test="heightMm != null">height_mm = #{heightMm},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIdPhotoPrintParamsById" parameterType="Long">
        delete from id_photo_print_params where id = #{id}
    </delete>

    <delete id="deleteIdPhotoPrintParamsByIds" parameterType="String">
        delete from id_photo_print_params where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
