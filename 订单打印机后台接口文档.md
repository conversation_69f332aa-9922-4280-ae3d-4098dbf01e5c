# 订单打印机后台管理接口文档

## 基础信息

- **基础URL**: `http://localhost:8081`
- **认证方式**: <PERSON><PERSON> (需要登录获取token)
- **Content-Type**: `application/json`

## 1. 订单管理接口

### 1.1 查询订单列表

**接口地址**: `GET /order/printer/list`

**权限要求**: `order:printer:list`

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页记录数，默认10 |
| deviceId | String | 否 | 设备ID筛选 |
| deviceName | String | 否 | 设备名称筛选 |
| orderStatus | Integer | 否 | 订单状态筛选 |
| openid | String | 否 | 用户openid筛选 |
| phone | String | 否 | 手机号筛选 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "orderId": "abc123",
      "deviceId": "printer001",
      "deviceName": "一楼打印机",
      "userId": 1001,
      "openid": "oZIa06iUqoN-SriKCPaD5n8LIRJ4",
      "phone": "13800138000",
      "orderStatus": 1,
      "totalAmount": 500,
      "payTime": "2024-01-30 14:20:00",
      "createTime": "2024-01-30 14:15:00"
    }
  ],
  "total": 1
}
```

### 1.2 查看订单详情

**接口地址**: `GET /order/printer/{orderId}`

**权限要求**: `order:printer:query`

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| orderId | String | 是 | 订单ID |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "orderId": "abc123",
    "deviceId": "printer001",
    "deviceName": "一楼打印机",
    "userId": 1001,
    "openid": "oZIa06iUqoN-SriKCPaD5n8LIRJ4",
    "phone": "13800138000",
    "orderStatus": 1,
    "totalAmount": 500,
    "payTime": "2024-01-30 14:20:00",
    "createTime": "2024-01-30 14:15:00"
  }
}
```

### 1.3 新增订单

**接口地址**: `POST /order/printer`

**权限要求**: `order:printer:add`

**请求体**:
```json
{
  "orderId": "abc123",
  "deviceId": "printer001",
  "deviceName": "一楼打印机",
  "openid": "oZIa06iUqoN-SriKCPaD5n8LIRJ4",
  "phone": "13800138000",
  "orderStatus": 0
}
```

### 1.4 修改订单

**接口地址**: `PUT /order/printer`

**权限要求**: `order:printer:edit`

**请求体**:
```json
{
  "orderId": "abc123",
  "deviceName": "二楼打印机",
  "orderStatus": 1,
  "remark": "修改备注"
}
```

### 1.5 删除订单

**接口地址**: `DELETE /order/printer/{orderIds}`

**权限要求**: `order:printer:remove`

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| orderIds | String | 是 | 订单ID，多个用逗号分隔 |

### 1.6 导出订单列表

**接口地址**: `POST /order/printer/export`

**权限要求**: `order:printer:export`

**请求参数**: 与查询列表接口相同

**响应**: Excel文件下载

## 2. 打印任务管理接口

### 2.1 查询订单的打印任务

**接口地址**: `GET /order/printer/tasks/{orderId}`

**权限要求**: 无

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| orderId | String | 是 | 订单ID |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "taskId": "task123",
      "orderId": "abc123",
      "deviceId": "printer001",
      "fileUrl": "http://example.com/file.pdf",
      "fileName": "document.pdf",
      "printStatus": 0,
      "copies": 2,
      "colorMode": 0,
      "taskPrice": 2.5,
      "createTime": "2024-01-30 14:15:00"
    }
  ]
}
```

### 2.2 更新打印任务状态

**接口地址**: `POST /order/printer/task/status`

**权限要求**: 无

**请求体**:
```json
{
  "taskId": "task123",
  "status": 2,
  "errorMsg": "打印完成"
}
```

**状态说明**:
- 0: 待打印
- 1: 打印中
- 2: 打印完成
- 3: 打印失败

## 3. 用户订单查询接口

### 3.1 查询用户订单列表（新版）

**接口地址**: `GET /order/printer/user`

**权限要求**: 需要用户token

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| orderStatus | String | 否 | 订单状态筛选 |

**订单状态说明**:
| 值 | 含义 | 说明 |
|----|------|------|
| 不传 | 全部 | 查询所有状态订单 |
| 0 | 未支付 | 待支付订单 |
| 1 | 已支付 | 已支付未打印 |
| 2 | 已取消 | 已取消订单 |
| 3 | 已退款 | 已退款订单 |
| 4 | 已打印 | 打印完成订单 |
| 5 | 打印中 | 正在打印订单 |
| 6 | 打印失败 | 打印失败订单 |
| 1,5 | 进行中 | 已支付+打印中 |

**测试示例**:
```bash
# 查询所有订单
GET /order/printer/user

# 查询未支付订单
GET /order/printer/user?orderStatus=0

# 查询已完成订单
GET /order/printer/user?orderStatus=4

# 查询进行中订单
GET /order/printer/user?orderStatus=1,5
```

## 4. 文件上传和订单创建接口

### 4.1 上传文件并创建订单

**接口地址**: `POST /order/printer/uploadAndOrder`

**权限要求**: 无

**请求类型**: `multipart/form-data`

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| file | File | 是 | 上传的文件 |
| orderId | String | 是 | 订单ID |
| deviceId | String | 是 | 设备ID |
| deviceName | String | 是 | 设备名称 |
| openid | String | 是 | 用户openid |
| phone | String | 否 | 手机号 |
| copies | Integer | 否 | 打印份数，默认1 |
| colorMode | Integer | 否 | 颜色模式，0-黑白，1-彩色 |
| duplexMode | Integer | 否 | 双面模式，0-单面，1-双面 |
| paperType | Integer | 否 | 纸张类型，1-A4，2-A5，3-照片纸 |
| pageRange | String | 否 | 页码范围，如"1-3,5" |
| isLastUpload | Boolean | 否 | 是否最后一次上传 |

## 5. 支付相关接口

### 5.1 支付回调处理

**接口地址**: `POST /order/printer/payNotify`

**权限要求**: 无

**请求体**:
```json
{
  "orderId": "abc123",
  "transactionId": "wx123456789"
}
```

## 6. 测试用例

### 6.1 完整订单流程测试

```bash
# 1. 查询订单列表
curl -X GET "http://localhost:8081/order/printer/list?pageNum=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 2. 查看订单详情
curl -X GET "http://localhost:8081/order/printer/abc123" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 3. 查询打印任务
curl -X GET "http://localhost:8081/order/printer/tasks/abc123"

# 4. 更新任务状态
curl -X POST "http://localhost:8081/order/printer/task/status" \
  -H "Content-Type: application/json" \
  -d '{"taskId":"task123","status":2}'

# 5. 查询用户订单（需要用户token）
curl -X GET "http://localhost:8081/order/printer/user?orderStatus=4" \
  -H "Authorization: Bearer USER_TOKEN"
```

### 6.2 权限测试

确保以下权限配置正确：
- `order:printer:list` - 查询订单列表
- `order:printer:query` - 查看订单详情
- `order:printer:add` - 新增订单
- `order:printer:edit` - 修改订单
- `order:printer:remove` - 删除订单
- `order:printer:export` - 导出订单

## 7. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 8. 注意事项

1. 所有需要权限的接口都需要在请求头中携带有效的token
2. 用户相关接口需要用户token，管理员接口需要管理员token
3. 文件上传接口支持PDF、DOC、DOCX、JPG、PNG等格式
4. 订单状态变更有严格的流程控制
5. 删除操作为逻辑删除，不会真正删除数据
